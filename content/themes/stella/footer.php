<?php do_action("houston_before_footer") ?>
<footer id="footer" class="footer" role="contentinfo">
    <div class="container">
        <div class="footer__wrapper">
            <a class="footer__logo" href="<?php echo $websiteurl; ?>" title="<?php bloginfo('description') ?>">
                <?php
                $turl = get_stylesheet_directory_uri();
                $logo =  $turl . '/assets/public/img/logo-white.svg';
                load_module('/assets/public/img/logo-white.svg');
                ?>
            </a>
            <div class="footer__item">
                <?php echo apply_filters('manipulate_nav_output', wp_nav_menu(array(
                    'theme_location' => 'primary_navigation',
                    'menu_class'     => 'footer-menu',
                    'echo'           => false,
                    'depth'          => 1,
                ))) ?>
            </div>
            <div class="footer__item">
            </div>
            <div class="footer__item info">
                <?php if (!empty(get_field('email', 'option'))) : ?>
                    <a class="footer__email" href="mailto:<?= get_field('email', 'option'); ?>"><?= get_field('email', 'option'); ?></a>
                <?php endif; ?>

                <?= get_field('address', 'option'); ?>
            </div>
            <div class="social">
                <?php if (!empty(get_field('facebook_link', 'option'))) : ?>
                    <a class="" href="<?= get_field('facebook_link', 'option'); ?>" target="_blank">
                        <span class="material-icons"><svg xmlns="http://www.w3.org/2000/svg" fill="white" width="22" height="22" viewBox="0 0 24 26">
                                <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z" />
                            </svg></span>
                    </a>
                <?php endif; ?>
                <?php if (!empty(get_field('instagram_link', 'option'))) : ?>
                    <a class="link-list description negative" href="<?= get_field('instagram_link', 'option'); ?>" target="_blank">
                        <span class="material-icons">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000" width="22px" height="22px" fill="black" stroke="black" stroke-width="30">
                                <path d="M295.42,6c-53.2,2.51-89.53,11-121.29,23.48-32.87,12.81-60.73,30-88.45,57.82S40.89,143,28.17,175.92c-12.31,31.83-20.65,68.19-23,121.42S2.3,367.68,2.56,503.46,3.42,656.26,6,709.6c2.54,53.19,11,89.51,23.48,121.28,12.83,32.87,30,60.72,57.83,88.45S143,964.09,176,976.83c31.8,12.29,68.17,20.67,121.39,23s70.35,2.87,206.09,2.61,152.83-.86,206.16-3.39S799.1,988,830.88,975.58c32.87-12.86,60.74-30,88.45-57.84S964.1,862,976.81,829.06c12.32-31.8,20.69-68.17,23-121.35,2.33-53.37,2.88-70.41,2.62-206.17s-.87-152.78-3.4-206.1-11-89.53-23.47-121.32c-12.85-32.87-30-60.7-57.82-88.45S862,40.87,829.07,28.19c-31.82-12.31-68.17-20.7-121.39-23S637.33,2.3,501.54,2.56,348.75,3.4,295.42,6m5.84,903.88c-48.75-2.12-75.22-10.22-92.86-17-23.36-9-40-19.88-57.58-37.29s-28.38-34.11-37.5-57.42c-6.85-17.64-15.1-44.08-17.38-92.83-2.48-52.69-3-68.51-3.29-202s.22-149.29,2.53-202c2.08-48.71,10.23-75.21,17-92.84,9-23.39,19.84-40,37.29-57.57s34.1-28.39,57.43-37.51c17.62-6.88,44.06-15.06,92.79-17.38,52.73-2.5,68.53-3,202-3.29s149.31.21,202.06,2.53c48.71,2.12,75.22,10.19,92.83,17,23.37,9,40,19.81,57.57,37.29s28.4,34.07,37.52,57.45c6.89,17.57,15.07,44,17.37,92.76,2.51,52.73,3.08,68.54,3.32,202s-.23,149.31-2.54,202c-2.13,48.75-10.21,75.23-17,92.89-9,23.35-19.85,40-37.31,57.56s-34.09,28.38-57.43,37.5c-17.6,6.87-44.07,15.07-92.76,17.39-52.73,2.48-68.53,3-202.05,3.29s-149.27-.25-202-2.53m407.6-674.61a60,60,0,1,0,59.88-60.1,60,60,0,0,0-59.88,60.1M245.77,503c.28,141.8,115.44,256.49,257.21,256.22S759.52,643.8,759.25,502,643.79,245.48,502,245.76,245.5,361.22,245.77,503m90.06-.18a166.67,166.67,0,1,1,167,166.34,166.65,166.65,0,0,1-167-166.34" transform="translate(-2.5 -2.5) " />
                            </svg></span>
                    </a>
                <?php endif; ?>
                <?php if (!empty(get_field('linkedin_link', 'option'))) : ?>
                    <a class="" href="<?= get_field('linkedin_link', 'option'); ?>" target="_blank">
                        <span class="material-icons"><svg xmlns="http://www.w3.org/2000/svg" fill="white" width="22" height="22" viewBox="0 0 24 26">
                                <path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z" />
                            </svg></span>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</footer>
<div class="copyright">
    <div class="container">
        <span>© Copyright Kraftstaden AB <?= date("Y") ?>. <br> Webbplatsen använder <a href="https://www.kraftstaden.se/cookies">cookies</a> för att förbättra upplevelsen.</span>
        <div class="social">
            <?php if (!empty(get_field('facebook_link', 'option'))) : ?>
                <a class="" href="<?= get_field('facebook_link', 'option'); ?>" target="_blank">
                    <span class="material-icons"><svg xmlns="http://www.w3.org/2000/svg" fill="white" width="22" height="22" viewBox="0 0 24 26">
                            <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z" />
                        </svg></span>
                </a>
            <?php endif; ?>
            <?php if (!empty(get_field('instagram_link', 'option'))) : ?>
                <a class="link-list description negative" href="<?= get_field('instagram_link', 'option'); ?>" target="_blank">
                    <span class="material-icons">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000" width="22px" height="22px" fill="black" stroke="black" stroke-width="30">
                            <path d="M295.42,6c-53.2,2.51-89.53,11-121.29,23.48-32.87,12.81-60.73,30-88.45,57.82S40.89,143,28.17,175.92c-12.31,31.83-20.65,68.19-23,121.42S2.3,367.68,2.56,503.46,3.42,656.26,6,709.6c2.54,53.19,11,89.51,23.48,121.28,12.83,32.87,30,60.72,57.83,88.45S143,964.09,176,976.83c31.8,12.29,68.17,20.67,121.39,23s70.35,2.87,206.09,2.61,152.83-.86,206.16-3.39S799.1,988,830.88,975.58c32.87-12.86,60.74-30,88.45-57.84S964.1,862,976.81,829.06c12.32-31.8,20.69-68.17,23-121.35,2.33-53.37,2.88-70.41,2.62-206.17s-.87-152.78-3.4-206.1-11-89.53-23.47-121.32c-12.85-32.87-30-60.7-57.82-88.45S862,40.87,829.07,28.19c-31.82-12.31-68.17-20.7-121.39-23S637.33,2.3,501.54,2.56,348.75,3.4,295.42,6m5.84,903.88c-48.75-2.12-75.22-10.22-92.86-17-23.36-9-40-19.88-57.58-37.29s-28.38-34.11-37.5-57.42c-6.85-17.64-15.1-44.08-17.38-92.83-2.48-52.69-3-68.51-3.29-202s.22-149.29,2.53-202c2.08-48.71,10.23-75.21,17-92.84,9-23.39,19.84-40,37.29-57.57s34.1-28.39,57.43-37.51c17.62-6.88,44.06-15.06,92.79-17.38,52.73-2.5,68.53-3,202-3.29s149.31.21,202.06,2.53c48.71,2.12,75.22,10.19,92.83,17,23.37,9,40,19.81,57.57,37.29s28.4,34.07,37.52,57.45c6.89,17.57,15.07,44,17.37,92.76,2.51,52.73,3.08,68.54,3.32,202s-.23,149.31-2.54,202c-2.13,48.75-10.21,75.23-17,92.89-9,23.35-19.85,40-37.31,57.56s-34.09,28.38-57.43,37.5c-17.6,6.87-44.07,15.07-92.76,17.39-52.73,2.48-68.53,3-202.05,3.29s-149.27-.25-202-2.53m407.6-674.61a60,60,0,1,0,59.88-60.1,60,60,0,0,0-59.88,60.1M245.77,503c.28,141.8,115.44,256.49,257.21,256.22S759.52,643.8,759.25,502,643.79,245.48,502,245.76,245.5,361.22,245.77,503m90.06-.18a166.67,166.67,0,1,1,167,166.34,166.65,166.65,0,0,1-167-166.34" transform="translate(-2.5 -2.5) " />
                        </svg></span>
                </a>
            <?php endif; ?>
            <?php if (!empty(get_field('linkedin_link', 'option'))) : ?>
                <a class="" href=" <?= get_field('linkedin_link', 'option'); ?>" target="_blank">
                    <span class="material-icons">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="white" width="22" height="22" viewBox="0 0 24 26">
                            <path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z" />
                        </svg></span>
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php do_action("houston_after_footer") ?>
<?php load_module('includes/parts/cookiebar.php'); ?>
<?php get_template_part('parts/general/footer-scripts'); ?>
<?php wp_footer(); ?>

<script type="text/javascript">
    WebFontConfig = {
        google: {
            families: ['Material+Icons']
        },
        active: function(e) {
            var className = document.getElementsByTagName('html')[0].className
            console.log('Fonts Loaded')
            document.getElementsByTagName('html')[0].className = className.replace('wf-not-active', '')
        }
    };
    (function() {
        var wf = document.createElement('script')
        wf.src = 'https://ajax.googleapis.com/ajax/libs/webfont/1/webfont.js'
        wf.type = 'text/javascript'
        wf.async = 'true'
        var s = document.getElementsByTagName('script')[0]
        s.parentNode.insertBefore(wf, s)
    })()
</script>

<!-- Siteimprove Analytics -->
<script type="text/javascript">
    /*<![CDATA[*/
    (function() {
        var sz = document.createElement('script');
        sz.type = 'text/javascript';
        sz.async = true;
        sz.src = '//siteimproveanalytics.com/js/siteanalyze_8055.js';
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(sz, s);
    })();
    /*]]>*/
</script>
<!-- End Siteimprove Analytics -->


</body>

</html>