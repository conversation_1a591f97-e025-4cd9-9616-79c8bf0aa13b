<?php
//Set variables for performance
$turl = get_stylesheet_directory_uri();
?>
<!doctype html>
<!--[if lt IE 7]>
<html class="no-js ie6 oldie" lang="sv_SE">    <![endif]-->
<!--[if IE 7]>
<html class="no-js ie7 oldie" lang="sv_SE">    <![endif]-->
<!--[if IE 8]>
<html class="no-js ie8 oldie" lang="sv_SE">    <![endif]-->
<!--[if gt IE 8]>	<!-->
<html class="no-js wf-not-active" lang="sv_SE">        <!--<![endif]-->
<head>
    <meta charset="<?php bloginfo( 'charset' ) ?>">
    <title><?php wp_title( '|', true, 'right' ) ?></title>

    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Favicon and Feed -->
    <link rel="shortcut icon" type="image/png" href="<?php echo $turl ?>/favicon.png">
    <link rel="alternate" type="application/rss+xml" title="<?php bloginfo( 'name' ) ?> Feed"
          href="<?php echo home_url() ?>/feed/">

	<?php if(is_search()): ?>
	<!-- Disable search results -->
	<meta name="robots" content="noindex,follow"/>
	<?php endif; ?>
    
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons"
      rel="stylesheet">
	
    <?php

	if ( function_exists( 'is_ios' ) && is_ios() ): ?>

        <link rel="apple-touch-icon" href="touch-icon-iphone.png"/>
        <link rel="apple-touch-icon" sizes="72x72" href="touch-icon-ipad.png"/>
        <link rel="apple-touch-icon" sizes="114x114" href="touch-icon-iphone-retina.png"/>
        <link rel="apple-touch-icon" sizes="144x144" href="touch-icon-ipad-retina.png"/>

	<?php endif ?>

	<?php wp_head() ?>

</head>
<?php 
$show_top = get_field('show_top');
$body_classes = ($show_top) ? '' : 'hide-hero';
if (get_post_meta( $post->post_parent, '_wp_page_template', true ) == 'tpl-hub-slimmed-down.php'):
    $body_classes .= ' hub-slimmed-down';
endif;
if( $_SERVER['HTTP_HOST'] == 'innovatum.kraftstaden.se'):
    $websiteurl = 'https://www.kraftstaden.se';
    $body_classes .= ' hub-slimmed-down';
else:
    $websiteurl = get_site_url();
endif;
?>
<body <?php body_class($body_classes) ?>>

<?php if (get_post_meta( $post->post_parent, '_wp_page_template', true ) == 'tpl-hub.php') :
    $tpl_hub = 'tpl-hub';
endif; ?>

<header class="site-header section section--no-padding <?php if ( is_page_template( 'tpl-campaign.php' ) ) : echo 'tpl-campaign'; endif; echo $tpl_hub; ?>">
	<div class="container">
		<div class="site-header__inner header__main">
			<a id="logo" class="site-header__logo" href="<?php echo $websiteurl; ?>"
               title="<?php bloginfo( 'description' ) ?>">
                <?php 
                $logo =  $turl . '/assets/public/img/logo.svg';   
                load_module('/assets/public/img/logo.svg'); 
                ?>
			</a>
            
            <div class="site-header__right-wrapper">
                <div class="site-header__menu-desktop header__main__navigation"> 
                    <?php echo apply_filters( 'manipulate_nav_output', wp_nav_menu( array(
                        'theme_location' => 'primary_navigation',
                        'menu_class'     => 'desktop-menu',
                        'echo'           => false,
                        'walker'         => new Wrapper_Walker_Nav_Menu(),
                    ) ) ) ?>
                </div>

                <?php if ( is_page_template( 'tpl-campaign.php' ) ) : ?>
                    <div class="tpl-campaign__button">
                        <a class="button button--search" href="<?php echo home_url('/'); ?>"><span class="button-text">Gå till kraftstaden.se</span> <span class="material-icons">arrow_forward</span></a>
                    </div>
                <?php endif; ?>

                <div class="site-header__search">
                    <a id="searchtoggle" class="button button--search"><span class="material-icons">search</span> <span class="search-text">Sök</span></a>
                    <div class="search-container">
                        <div class="container">
                            <div class="search-form">
                                <?php get_search_form(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

	        <div class="site-header__menu-toggle" data-toggle-menu="true">
		        <i class="material-icons" translate="no">menu</i>
            </div>    
            <div class="site-header__menu-close" data-toggle-menu="true">
			    <i class="material-icons" translate="no">close</i>
		    </div>

		</div>
    
    </div>
    
    <div class="site-header__menu-mobile">    
                    <div class="search-form">
                        <?php get_search_form(); ?>
                    </div>
        <?php wp_nav_menu( array(
            'container'       => false,
            'container_class' => false,
            'menu_class'      => 'mobile-menu',
            'theme_location'  => 'mobile_navigation',
        ) ) ?>
	</div>

</header>
