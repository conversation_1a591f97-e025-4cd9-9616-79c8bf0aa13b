<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date: 2016-11-09
 * Time: 14:48
 */

namespace Helpers;


class CPT {
	private $s_name = "";
	private $name = "";
	private $slug = "";
	private $type = "";
	private $domain = "";
	private $args = [];
	private $taxonomies = [];

	/**
	 * CPT constructor.
	 * @param $type
	 * @param array $cpt_settings
	 * @param array $args
	 */
	public function __construct($type, array $cpt_settings, array $args = []) {
		$this->type = $type;
		foreach ($cpt_settings as $key => $cpt_setting) {
			// Only set properties that are declared or commonly used
			if (property_exists($this, $key)) {
				$this->$key = $cpt_setting;
			}
		}
		if ($this->s_name == "") $this->s_name = $this->name;
		if ($this->slug == "") $this->slug = sanitize_title($this->name);
		$this->args = $args;
	}

	/**
	 * @return string
	 */
	public function get_domain() {
		return $this->domain;
	}

	/**
	 * @param string $domain
	 * @return $this
	 */
	public function set_domain($domain) {
		$this->domain = $domain;
		return $this;
	}

	/**
	 * @return string
	 */
	public function get_type() {
		return $this->type;
	}

	/**
	 * @param array $taxonomies
	 * @return CPT
	 */
	public function set_taxonomies(array $taxonomies) {
		$this->taxonomies = $taxonomies;
		return $this;
	}

	/**
	 * @param $taxonomy
	 * @return $this
	 */
	public function add_taxonomy($taxonomy) {
		$this->taxonomies[] = $taxonomy;
		return $this;
	}

	public function get_taxonomies() {
		return array_unique($this->taxonomies);
	}

	public function get_register_values() {
		$s_name = $this->s_name;
		$p_name = $this->name;
		$domain = $this->domain;
		$slug = ($this->slug != "") ? $this->slug : $this->type;

		$labels = array(
			'name'                => _x( ucfirst($p_name), 'Post Type General Name', $domain ),
			'singular_name'       => _x( ucfirst($s_name), 'Post Type Singular Name', $domain ),
			'menu_name'           => __( ucfirst($p_name), $domain ),
			'name_admin_bar'      => __( ucfirst($p_name), $domain ),
			'parent_item_colon'   => __( 'Parent Item:', $domain ),
			'all_items'           => __( 'Alla '.$p_name, $domain ),
			'add_new_item'        => __( 'Lägg till '.$s_name, $domain ),
			'add_new'             => __( 'Lägg till nytt', $domain ),
			'new_item'            => __( 'Ny '.$s_name, $domain ),
			'edit_item'           => __( 'Redigera '.$s_name, $domain ),
			'update_item'         => __( 'Uppdatera '.$s_name, $domain ),
			'view_item'           => __( 'Visa '.$s_name, $domain ),
			'search_items'        => __( 'Sök '.$p_name, $domain ),
			'not_found'           => __( 'Hittas inte', $domain ),
			'not_found_in_trash'  => __( 'Hittas inte i papperskorgen', $domain ),
		);

		return array_merge([
			'label'               => __( ucfirst($p_name), $domain ),
			'description'         => __( ucfirst($p_name), $domain ),
			'labels'              => $labels,
			'supports'            => ['title','editor','thumbnail','revisions'],
            'taxonomies'          => array_unique($this->taxonomies),
			'hierarchical'        => false,
			'public'              => true,
			'show_ui'             => true,
			'show_in_menu'        => true,
			'menu_position'       => 5,
			'menu_icon'           => 'dashicons-admin-users',
			'show_in_admin_bar'   => true,
			'show_in_nav_menus'   => true,
			'can_export'          => true,
			'has_archive'         => true,
			'exclude_from_search' => false,
			'publicly_queryable'  => true,
			'capability_type'     => 'page',
			'rewrite'             => ['slug' => $slug]
		], $this->args);
	}
}