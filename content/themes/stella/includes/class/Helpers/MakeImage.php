<?php
namespace Helpers;

/**
 * Class MakeImage
 * @package Helpers
 */
class MakeImage
{


	public $attach_id;
	public $image_src;
	public $file_path;
	public $image_meta = [];
	public $gif = false;
	public $srcset_sizes = [];


	/**
	 * @param $src
	 * @param $width
	 * @param $height
	 * @param bool $crop
	 * @param bool $return_array
	 * @return array|mixed|string|void
	 */
	public function create($src, $width, $height, $crop = false, $return_array = false) {

		if ( empty( $src ) ) {
			return ($return_array) ? [] : '';
		}

		$is_relative_protocol = strpos( $src, '//' ) === 0;
		if ( $is_relative_protocol ) {
			$src = str_replace( '//', get_protocol(), $src );
		}

		$attach_id = $src;
		$img_url = $src;
		$this->get_data($src, $attach_id, $img_url);

		// If gif return url
		if ($this->gif)  {
			$url = ($this->attach_id && !empty($this->image_src)) ? $this->image_src[0] : $img_url;
			if ($return_array) {
				$vt_image = ['url' => $url];
				return $this->add_webp_to_image_array($vt_image);
			}
			return $url;
		}

		// Validate file path before calling pathinfo
		if (empty($this->file_path) || !is_string($this->file_path)) {
			return ($return_array) ? [] : '';
		}

		$file_info = pathinfo( $this->file_path  );

		// check if file exists and has extension
		if (!isset($file_info['extension']) || !isset($file_info['filename']) || !isset($file_info['dirname'])) {
			return ($return_array) ? [] : '';
		}

		$base_file = $file_info[ 'dirname' ] . '/' . $file_info[ 'filename' ] . '.' . $file_info[ 'extension' ];
		if ( ! file_exists( $base_file ) ) {
			return ($return_array) ? [] : '';
		}

		$extension = '.' . $file_info[ 'extension' ];
		// the image path without the extension
		$no_ext_path = $file_info[ 'dirname' ] . '/thumbnails/' . $file_info[ 'filename' ];

		if ( ! is_numeric( $attach_id ) ) {
			global $wpdb;
			$query = "SELECT ID FROM {$wpdb->posts} WHERE guid='$attach_id'";
			$attach_id = $wpdb->get_var( $query );
		}

		// $attach_id = apply_filters('sanitize_filename',$attach_id);


		$cropped_img_path = $no_ext_path . '-' . $attach_id . '-' . $width . 'x' . $height . $extension;
		// checking if the file size is larger than the target size
		// if it is smaller or the same size, stop right here and return


		if ( $this->image_src[ 1 ] >= $width ) {

			// the file is larger, check if the resized version already exists (for $crop = true but will also work for $crop = false if the sizes match)
			if ( file_exists( $cropped_img_path ) ) {
				$cropped_img_url = str_replace( basename( $this->image_src[ 0 ] ), 'thumbnails/' . basename( $cropped_img_path ), $this->image_src[ 0 ] );

				$vt_image = array(
					'url' => $cropped_img_url,
					'width' => $width,
					'height' => $height,
					'meta' => $this->image_meta
				);

				if ($return_array) {
					return $this->add_webp_to_image_array($vt_image);
				}
				return $vt_image['url'];
			}
			// $crop = false or no height set
			if ( $crop == false OR ! $height ) {
				// calculate the size proportionaly
				$proportional_size = wp_constrain_dimensions( $this->image_src[ 1 ], $this->image_src[ 2 ], $width, $height );
				$resized_img_path = $no_ext_path . '-' . $proportional_size[ 0 ] . 'x' . $proportional_size[ 1 ] . $extension;
				// checking if the file already exists
				if ( file_exists( $resized_img_path ) ) {
					$resized_img_url = str_replace( basename( $this->image_src[ 0 ] ), 'thumbnails/' . basename( $resized_img_path ), $this->image_src[ 0 ] );
					$vt_image = array(
						'url' => $resized_img_url,
						'width' => $proportional_size[ 0 ],
						'height' => $proportional_size[ 1 ],
						'meta' => $this->image_meta
					);

					if ($return_array) {
						return $this->add_webp_to_image_array($vt_image);
					}
					return $vt_image['url'];
				}
			}
			// check if image width is smaller than set width
			$img_size = getimagesize( $this->file_path );
			if ( $img_size[ 0 ] <= $width ) $width = $img_size[ 0 ];
			// Check if GD Library installed
			if ( ! function_exists( 'imagecreatetruecolor' ) ) {
				echo 'GD Library Error: imagecreatetruecolor does not exist - please contact your webhost and ask them to install the GD library';
				return;
			}
			// no cache files - let's finally resize it


			$image = wp_get_image_editor( $this->file_path );
			if ( ! is_wp_error( $image ) ) {
				$image->set_quality( 100 );
				$image->resize( $width, $height, $crop );
				$final_image = $image->save( $cropped_img_path );
			}

			//$new_img_path = image_resize($file_path, $width, $height, $crop);
			//$new_img_size = getimagesize($new_img_path);
			$image_quality = (defined('MAKE_IMAGE_QUALITY')) ? \MAKE_IMAGE_QUALITY : 75;
			list( $orig_w, $orig_h, $orig_type ) = @getimagesize( $final_image[ 'path' ] );
			switch ( $orig_type ) {
				case IMAGETYPE_GIF:
					break;
				case IMAGETYPE_PNG:
					break;
				case IMAGETYPE_JPEG:
					if(extension_loaded('imagick')) {
						try {
							$imagick = new \Imagick();
							$imagick->readImage( $final_image[ 'path' ]);
							// Here you can sharpen image quality
							if (defined('MAKE_IMAGE_QUALITY') && defined('MAKE_IMAGE_SHARPER') && \MAKE_IMAGE_SHARPER) {
								$imagick->unsharpMaskImage(0, 0.5, 1, 0);
							}
							$imagick->setInterlaceScheme(\Imagick::INTERLACE_PLANE);
							$imagick->setCompression(\Imagick::COMPRESSION_JPEG);
							$imagick->setCompressionQuality($image_quality);
							$imagick->setImageCompressionQuality($image_quality);
							$imagick->writeImage( $final_image[ 'path' ]);
							$imagick->destroy();
						} catch (Exception $exception) {
						}
					} else {
						$image = imagecreatefromstring( file_get_contents( $final_image['path'] ) );
						imageinterlace($image, true);
						imagejpeg($image, $final_image['path'], $image_quality);
						imagedestroy($image);
					}
					break;
			}

			$new_img = str_replace( basename( $this->image_src[ 0 ] ), 'thumbnails/' . basename( $final_image[ 'path' ] ), $this->image_src[ 0 ] );


			// resized output
			$vt_image = array(
				'url' => $new_img,
				'width' => $final_image[ 'width' ],
				'height' => $final_image[ 'height' ],
				'meta' => $this->image_meta
			);

			if ($return_array) {
				return $this->add_webp_to_image_array($vt_image);
			}
			return $vt_image['url'];
		}
		// default output - without resizing
		$vt_image = array(
			'url' => $this->image_src[ 0 ],
			'width' => $width,
			'height' => $height,
			'meta' => $this->image_meta
		);

		if ($return_array) {
			return $this->add_webp_to_image_array($vt_image);
		}
		return $vt_image['url'];
	}


	/**
	 * @param $src
	 * @param bool $crop
	 * @return string
	 */
	public function create_srcset($src, $crop = false) {

		if ( empty( $src ) ) {
			return '';
		}

		$attach_id = $src;
		$img_url = $src;

		// If data is not set
		if (!$this->image_src) {
			$this->get_data($src, $attach_id, $img_url);
		}

		// If gif return url
		if ($this->gif)  {
			return ($this->attach_id && !empty($this->image_src)) ? $this->image_src[0] : $src;
		}

		if (!file_exists( $this->file_path )) {
			return '';
		}

		$orig_size = getimagesize( $this->file_path );
		$width = $orig_size[0];
		$height = $orig_size[1];
		$srcset = '';
		if (!empty($this->srcset_sizes)) {

			$atts = [];
			foreach ($this->srcset_sizes as $key => $size) {
				if ($width>$size[0] && $height>$size[1]) {
					$atts[] = $this->create($this->image_src[0], $size[0], $size[1], $crop) . ' '.$key;
				}
			}

			$srcset = implode(", ", $atts);
		}

		return $srcset;
	}


	/**
	 * @param $src
	 * @param $attach_id
	 * @param $img_url
	 */
	private function get_data($src, $attach_id, $img_url) {
		$type = '';
		if ( is_array( $src ) ) {
			$this->attach_id = $src[ 'id' ];
			$this->image_src = wp_get_attachment_image_src( $attach_id, 'full' );
			$this->file_path = get_attached_file( $attach_id );
			$this->image_meta = wp_get_attachment_metadata( $attach_id );
			$this->image_meta = array(
				"title" => get_the_title( $this->attach_id ),
				"alt" => get_post_meta( $this->attach_id, '_wp_attachment_image_alt', true ),
				"description" => get_the_content( $this->attach_id )
			);
			$type = get_post_mime_type($this->attach_id);

		} elseif ( ! filter_var( $src, FILTER_VALIDATE_URL ) ) {
			// if its not an URL assume its an ID
			// this is an attachment, so we have the ID
			$this->attach_id = $attach_id;
			$this->image_src = wp_get_attachment_image_src( $attach_id, 'full' );
			$this->file_path = get_attached_file( $attach_id );
			$this->image_meta = array(
				"title" => get_the_title( $attach_id ),
				"alt" => get_post_meta( $attach_id, '_wp_attachment_image_alt', true ),
				"description" => get_the_content( $attach_id )
			);
			$type = get_post_mime_type($this->attach_id);

		} else {

			// this is not an attachment, let's use the image url
			$file_path = parse_url( $img_url );
			$file_path = $_SERVER[ 'DOCUMENT_ROOT' ] . $file_path[ 'path' ];

			// Look for Multisite Path
			if ( file_exists( $file_path ) === false ) {
				global $blog_id;
				$parsed_url = parse_url( $img_url );
				$path = isset($parsed_url['path']) ? $parsed_url['path'] : '';

				if ( preg_match( '/files/', $path ) ) {
					$path_parts = explode( '/', $path );
					foreach ( $path_parts as $k => $v ) {
						if ( $v == 'files' ) {
							$path_parts[ $k - 1 ] = 'content/blogs.dir/' . $blog_id;
						}
					}
					$path = implode( '/', $path_parts );
				}
				$file_path = $_SERVER[ 'DOCUMENT_ROOT' ] . $path;
			}
			//$file_path = ltrim( $file_path['path'], '/' );
			//$file_path = rtrim( ABSPATH, '/' ).$file_path['path'];

			// Validate file path and get image size safely
			if (!file_exists($file_path) || is_dir($file_path)) {
				// If file doesn't exist or is a directory, set default values and return
				$this->attach_id = null;
				$this->image_src = array('', 0, 0);
				$this->file_path = '';
				$this->image_meta = array('title' => '');
				return;
			}

			$orig_size = @getimagesize( $file_path );
			if ($orig_size === false) {
				// If getimagesize fails, set default values and return
				$this->attach_id = null;
				$this->image_src = array('', 0, 0);
				$this->file_path = '';
				$this->image_meta = array('title' => '');
				return;
			}

			$type = isset($orig_size['mime']) ? $orig_size['mime'] : '';

			$file_info = pathinfo( $file_path );
			$this->attach_id = null;
			$this->image_src[ 0 ] = $img_url;
			$this->image_src[ 1 ] = isset($orig_size[0]) ? $orig_size[0] : 0;
			$this->image_src[ 2 ] = isset($orig_size[1]) ? $orig_size[1] : 0;
			$this->file_path = $file_path;
			$this->image_meta = [
				'title' => isset($file_info['basename']) ? $file_info['basename'] : ''
			];

		}

		// If GIF dont resize
		if ($type == 'image/gif') {
			$this->gif = true;
		}
	}


	/**
	 * Flush generated thumbnails
	 */
	static function flush_thumbnails() {
		$uploads_data = wp_upload_dir();
		self::_delete_thumbnails_recursive($uploads_data['basedir']);
	}


	/**
	 * Recursive delete thumbnails
	 */
	static function _delete_thumbnails_recursive($file) {
		$files = glob( $file . '*', GLOB_MARK );
		foreach( $files as $file ){
			if (is_dir($file)) {
				if (basename($file) == "thumbnails") {
					foreach (glob($file."/*.*") as $filename) {
						if (is_file($filename)) {
							unlink($filename);
						}
					}
					rmdir($file);
				}
				self::_delete_thumbnails_recursive(($file));
			}
		}
	}

	/**
	 * Check if WebP version of the image exists and return its URL
	 *
	 * @param string $image_url Original image URL
	 * @return string|false WebP URL if exists, false otherwise
	 */
	private function get_webp_url($image_url) {
		// Parse the URL to get the file path
		$parsed_url = parse_url($image_url);
		if (!isset($parsed_url['path'])) {
			return false;
		}

		$file_path = $_SERVER['DOCUMENT_ROOT'] . $parsed_url['path'];

		// Check for double extension (file.jpg.webp)
		$webp_file_double = $file_path . '.webp';
		if (file_exists($webp_file_double)) {
			return $image_url . '.webp';
		}

		return false;
	}

	/**
	 * Add WebP URL to image array if WebP version exists
	 *
	 * @param array $vt_image Image array with 'url' key
	 * @return array Image array with 'webp_url' key added if WebP exists
	 */
	private function add_webp_to_image_array($vt_image) {
		if (is_array($vt_image) && !empty($vt_image['url'])) {
			$webp_url = $this->get_webp_url($vt_image['url']);
			if ($webp_url) {
				$vt_image['webp_url'] = $webp_url;
			}
		}
		return $vt_image;
	}

}
