<?php
namespace Helpers;

/**
 * Class StellaHelper
 * @package Raket
 */
class StellaHelper
{

	/**
	 * @param $file
	 * @param bool $attributes
	 */
	static function load_module($file, $attributes = false){
		if($attributes){
			extract($attributes, EXTR_PREFIX_SAME, "module");
		}

		$template_path = locate_template($file);
		if (empty($template_path)) {
			error_log("StellaHelper::load_module - Template not found: " . $file);
			return;
		}

		include($template_path);
	}



}