<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date: 2016-11-09
 * Time: 14:53
 */

namespace Helpers;


class Taxonomy {
	private $type;
	private $related_cpts = [];
	private $s_name = "";
	private $name = "";
	private $slug = "";
	private $args;
	private $domain = "";

	// Properties that might be set dynamically from tax_settings
	public $show_in_rest;
	public $show_ui;
	public $show_admin_column;

	/**
	 * @param $type
	 * @param $related_cpts
	 * @param array $tax_settings
	 * @param array $args
	 */
	public function __construct($type, $related_cpts, array $tax_settings, array $args = []) {
		$this->type = $type;
		if (is_array($related_cpts)) {
			$this->related_cpts = $related_cpts;
		}
		else {
			$this->related_cpts[] = $related_cpts;
		}
		foreach ($tax_settings as $key => $tax_setting) {
			// Only set properties that are declared or commonly used
			if (property_exists($this, $key)) {
				$this->$key = $tax_setting;
			}
		}
		if ($this->s_name == "") $this->s_name = $this->name;
		if ($this->slug == "") $this->slug = sanitize_title($this->name);
		$this->args = $args;
	}

	public function get_register_values() {
		$s_name = $this->s_name;
		$p_name = $this->name;
		$domain = $this->domain;
		$slug = ($this->slug != "") ? $this->slug : $this->type;

		$labels = [
			'name'              => _x( ucfirst($p_name), 'taxonomy general name', $domain ),
			'singular_name'     => _x( ucfirst($s_name), 'taxonomy singular name', $domain ),
			'search_items'      => __( 'Sök '.$p_name, $domain ),
			'all_items'         => __( 'Alla '.$p_name, $domain ),
			'edit_item'         => __( 'Ändra '.$s_name, $domain ),
			'update_item'       => __( 'Updatera '.$s_name, $domain ),
			'add_new_item'      => __( 'Lägg till ny '.$s_name, $domain ),
			'menu_name'         => __( ucfirst($p_name), $domain ),
		];

		return array_merge([
			'hierarchical'      => false,
			'labels'            => $labels,
			'show_ui'           => true,
			'show_admin_column' => true,
			'query_var'         => true,
			'rewrite'           => ['slug' => $slug],
		], $this->args);
	}

	/**
	 * @param string $domain
	 * @return Taxonomy
	 */
	public function set_domain($domain) {
		$this->domain = $domain;
		return $this;
	}

	/**
	 * @return mixed
	 */
	public function get_related_cpts() {
		return $this->related_cpts;
	}

	/**
	 * @return mixed
	 */
	public function get_type() {
		return $this->type;
	}

	public function add_related_cpt($related_cpt) {
		$this->related_cpts[] = $related_cpt;
		return $this;
	}

	/**
	 * @param array $related_cpts
	 * @return Taxonomy
	 */
	public function set_related_cpts($related_cpts) {
		$this->related_cpts = $related_cpts;
		return $this;
	}
}