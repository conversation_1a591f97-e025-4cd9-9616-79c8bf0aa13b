<?php
namespace Stella;

/**
 * Class Stella
 * @package Stella
 */
class Setup
{
    public $turl;
    public $stylesheet_dir;
    public $stylesheet_uri;
    public $template_dir;
    public $template_uri;
    public $development;

    public function __construct()
    {
        $this->stylesheet_dir = get_stylesheet_directory();
        $this->stylesheet_uri = get_stylesheet_directory_uri();
        $this->template_dir = get_template_directory();
        $this->template_uri = get_template_directory_uri();

        //Are we in development or live enviroment?
        $this->development = (defined('WP_LOCAL_DEV') && WP_LOCAL_DEV) || strpos($_SERVER['HTTP_HOST'] . $_SERVER['PHP_SELF'], '.dev') !== false ? true : false;

        //Setup stella
        add_action('after_setup_theme', array($this, 'stella_parent_theme_setup'), 9);
        add_action('wp_enqueue_scripts', array($this, 'raket_add_scripts'));
    }


    /**
     * Setup Stella
     */
    public function stella_parent_theme_setup()
    {
        global $pagenow;
        // Config theme settings
        add_theme_support('html5', array('comment-list', 'comment-form', 'search-form', 'gallery', 'caption'));
        add_theme_support('menus');
        //add_theme_support( 'title-tag' );
        add_theme_support('clean-head');
	    add_theme_support('custom-login-logo');
        //add_theme_support('better-htaccess');
        add_theme_support('post-thumbnails');


        //---------------------------------------------------------------------------------
        //	Run activation tasks on theme activation
        //---------------------------------------------------------------------------------
        if (is_admin() && 'themes.php' === $pagenow && isset($_GET['activated'])) {
            new Activate();
        }
        new Extras();
        new General();
        new Images();
        new Performance();
        new Responsible();

        if (is_admin()) {
            new Admin();
        }

        //register navigation
        register_nav_menus(array(
            'primary_navigation' => __('Primary Navigation', 'stella_theme'),
            'mobile_navigation' => __('Mobile Primary Navigation', 'stella_theme'),
        ));
    }


    /**
     * Enqueue all Styles and scripts here.
     * Handle if we're in development or production mode.
     */
    public function raket_add_scripts()
    {	
        $base_path_dir = $this->template_dir . '/assets/dist/';
        $base_path_uri = $this->template_uri . '/assets/dist/';

        $paths = [
            'js' => $base_path_uri. 'app.js',
            'css' => $base_path_uri. 'app.css'
        ];
        
        // If production use prod builds
        if( !$this->development ) {
            $prod_paths = [
                'js' => $base_path_uri. 'app.min.js',
                'css' => $base_path_uri. 'app.min.css'
            ];
            $paths['js'] = file_exists( $base_path_dir. 'app.min.js' ) ? $prod_paths['js'] : $paths['js'];
            $paths['css'] = file_exists( $base_path_dir. 'app.min.css' ) ? $prod_paths['css'] : $paths['css'];
        }
        
        wp_enqueue_script('app', $paths['js'], array(), false, true);
        wp_enqueue_style('app', $paths['css']);
    }

}

