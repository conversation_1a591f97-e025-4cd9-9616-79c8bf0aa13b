<?php
/**
 * @param $src
 * @param $width
 * @param $height
 * @param bool $crop
 * @param array $srcset_sizes
 * @return array|mixed|string|void
 */
function get_make_image($src, $width, $height, $crop = false, $srcset_sizes = []) {
	$make_image = new Helpers\MakeImage();
	$image = $make_image->create($src, $width, $height, $crop, true);
	if (!empty($srcset_sizes)) {
		$make_image->srcset_sizes = $srcset_sizes;
		$image['srcset'] = $make_image->create_srcset($src, $crop);
	}
	return $image;
}

/**
 * @param $src
 * @param $width
 * @param $height
 * @param bool $crop
 * @param array $srcset_sizes
 */
function make_image($src, $width, $height, $crop = false, $srcset_sizes = []) {
	$image = get_make_image($src, $width, $height, $crop, $srcset_sizes);
	$srcset = '';
	if (!empty($image['srcset'])) {
		$srcset = 'srcset="'.$image['srcset'].'"';
	}
	$alt = (!empty($image['meta'])) ? $image['meta']['title'] : '';
	
	// Check if browser supports WebP
	$use_webp = false;
	if (!empty($image['webp_url']) && isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'image/webp') !== false) {
		$use_webp = true;
	}
	
	$img_url = $use_webp && !empty($image['webp_url']) ? $image['webp_url'] : $image['url'];
	echo '<img src="'.$img_url.'" '.$srcset.' alt="'.$alt.'" loading="lazy">';
}

/**
 * @param $src
 * @param $width
 * @param $height
 * @param bool $crop
 * @return mixed
 */
function get_make_image_url($src, $width, $height, $crop = false) {
	$image = get_make_image($src, $width, $height, $crop);
	
	// Check if image array is valid
	if (!is_array($image) || empty($image)) {
		return '';
	}

	// Check if browser supports WebP and WebP version is available
	$use_webp = false;
	if (!empty($image['webp_url']) && isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'image/webp') !== false) {
		$use_webp = true;
	}

	// Ensure we have valid URLs before returning
	$webp_url = isset($image['webp_url']) ? $image['webp_url'] : '';
	$regular_url = isset($image['url']) ? $image['url'] : '';

	return $use_webp && !empty($webp_url) ? $webp_url : $regular_url;
}

/**
 * @param $src
 * @param array $srcset_sizes
 * @param bool $crop
 * @return string
 */
function get_make_image_srcset($src, $srcset_sizes = [], $crop = false) {
	$make_image = new Helpers\MakeImage();
	if (!empty($srcset_sizes)) {
		$make_image->srcset_sizes = $srcset_sizes;
		return $make_image->create_srcset($src, $crop);
	}
	return '';
}


/**
 * Load module with attribute passing / Real magic is in StellaHelper
 */
function load_module($file, $attributes = false){
	if (class_exists('Helpers\StellaHelper')) {
		\Helpers\StellaHelper::load_module($file, $attributes);
	}
}
