<?php
/**
 * FeedWordPress PHP 8.1+ Compatibility Fixes
 * 
 * This file contains theme-based fixes for FeedWordPress plugin
 * to handle PHP 8.1+ deprecation warnings without modifying the plugin directly.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Simple and effective approach: Suppress FeedWordPress trim() deprecation warnings
 * 
 * This approach uses a custom error handler to specifically target and suppress
 * the trim() deprecation warnings from FeedWordPress while preserving all other errors.
 */
class FeedWordPress_Error_Suppressor {
    
    private static $original_error_handler = null;
    private static $is_active = false;
    
    /**
     * Initialize the error suppression
     */
    public static function init() {
        // Only apply if FeedWordPress is active
        if (class_exists('FeedWordPress')) {
            // Set up the custom error handler early
            add_action('plugins_loaded', array(__CLASS__, 'setup_error_handler'), 1);
            
            // Also suppress during admin operations
            if (is_admin()) {
                add_action('admin_init', array(__CLASS__, 'maybe_suppress_admin_errors'), 1);
            }
        }
    }
    
    /**
     * Set up the custom error handler
     */
    public static function setup_error_handler() {
        if (!self::$is_active) {
            self::$original_error_handler = set_error_handler(array(__CLASS__, 'custom_error_handler'));
            self::$is_active = true;
        }
    }
    
    /**
     * Custom error handler that suppresses specific FeedWordPress trim() warnings
     */
    public static function custom_error_handler($errno, $errstr, $errfile, $errline) {
        // Check if this is a trim() deprecation warning from FeedWordPress
        if ($errno === E_DEPRECATED && 
            strpos($errstr, 'trim()') !== false && 
            strpos($errstr, 'Passing null to parameter') !== false &&
            (strpos($errfile, 'feedwordpress') !== false || strpos($errfile, 'FeedWordPress') !== false)) {
            
            // Suppress this specific warning by returning true
            return true;
        }
        
        // For all other errors, call the original error handler if it exists
        if (self::$original_error_handler && is_callable(self::$original_error_handler)) {
            return call_user_func(self::$original_error_handler, $errno, $errstr, $errfile, $errline);
        }
        
        // If no original handler, use default behavior (return false)
        return false;
    }
    
    /**
     * Suppress errors during FeedWordPress admin operations
     */
    public static function maybe_suppress_admin_errors() {
        // Check if we're on a FeedWordPress admin page
        if (isset($_GET['page']) && strpos($_GET['page'], 'feedwordpress') !== false) {
            // Ensure our error handler is active
            self::setup_error_handler();
        }
    }
    
    /**
     * Restore original error handler (cleanup method)
     */
    public static function restore_error_handler() {
        if (self::$is_active && self::$original_error_handler) {
            set_error_handler(self::$original_error_handler);
            self::$is_active = false;
        }
    }
}

/**
 * Alternative simpler approach: Use WordPress error suppression
 * This is more reliable and doesn't interfere with other error handlers
 */
class FeedWordPress_Simple_Fix {

    public static function init() {
        if (class_exists('FeedWordPress')) {
            // Hook early to suppress errors during FeedWordPress operations
            add_action('init', array(__CLASS__, 'suppress_feedwordpress_errors'), 1);

            // Specifically target admin pages where FeedWordPress runs
            if (is_admin()) {
                add_action('admin_init', array(__CLASS__, 'suppress_admin_errors'), 1);
            }
        }
    }

    public static function suppress_feedwordpress_errors() {
        // Temporarily suppress deprecation warnings during WordPress init
        // when FeedWordPress might be running its methods
        $original_reporting = error_reporting();
        error_reporting($original_reporting & ~E_DEPRECATED);

        // Restore after init is complete
        add_action('wp_loaded', function() use ($original_reporting) {
            error_reporting($original_reporting);
        }, 999);
    }

    public static function suppress_admin_errors() {
        // Check if we're on a FeedWordPress page
        if (isset($_GET['page']) && strpos($_GET['page'], 'feedwordpress') !== false) {
            // Suppress deprecation warnings for the entire admin request
            error_reporting(error_reporting() & ~E_DEPRECATED);
        }
    }
}

// Initialize both approaches - the error handler is more precise,
// the simple fix is more reliable as a fallback
FeedWordPress_Error_Suppressor::init();
FeedWordPress_Simple_Fix::init();
