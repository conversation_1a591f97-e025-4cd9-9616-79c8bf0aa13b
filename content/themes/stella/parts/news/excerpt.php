<?php
$default = array(
    'excerpt' => '15',
    'img_width' => '360',
    'img_height' => '200'
);
if (isset($args)) {
    $args = array_merge($default, $args);
} else {
    $args = $default;
}

$post_type = get_post_type();
?>

<div class="object">
    <div class="object__wrapper">
        <a href="<?php the_permalink() ?>">      
            <?php 
            if ( get_the_post_thumbnail_url() ) : 
                $image = get_the_post_thumbnail_url('medium_large');
            elseif ( !empty(get_field('featured_image')) ) : 
                $image = get_make_image_url(get_field('featured_image'), 560, 315, true);
            else :
                $image = get_template_directory_uri() . '/assets/src/img/default-image-small.png';
            endif;    
            ?>
            <div class="object__image" style="background-image: url('<?= $image; ?>')"></div>

            <div class="object__content">
                <h5 class="object__title"> <?php the_title(); ?></h5>
                <div class="object__meta">
                    <?php
                    $icons = array(
                        '<span class="material-icons">local_offer</span> ',
                        '<span class="material-icons">room</span> '
                    );
                    $i = 0;
                    foreach (get_object_taxonomies(get_post_type(), 'objects') as $taxonomy):
                        $terms = implode(', ', array_map(function ($term) {
                            return $term->name;
                        }, wp_get_post_terms(get_the_ID(), $taxonomy->name)));
                        if ($terms != ""):
                            echo '<span class="meta__item">';
                            echo ($terms != "") ? $icons[$i] . ' ' . $terms : '';
                            echo '</span>';
                        endif;
                        $i++;
                    endforeach;
                    ?>
                </div>
            </div>
        </a>
    </div>
</div>