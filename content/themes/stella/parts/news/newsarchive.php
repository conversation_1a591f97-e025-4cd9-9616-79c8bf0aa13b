<div class="newsarchive">
        <?php if($heading): ?>
            <h3 class="newsarchive__title">
                <?= $heading; ?>
            </h3>
        <?php endif; ?>
        <?php $blogposts = get_posts(array(
            'posts_per_page' => 5
        ));

        if (!empty($blogposts)) {
            $first_post = array_slice($blogposts, 0, 1);
            $second_posts = array_slice($blogposts, 1, 4);
            $first_post = $first_post[0];

            // Validate first post
            if ($first_post && $first_post->ID && get_post_status($first_post->ID) !== false) {
        ?>
                <div class="newsarchive__col--left">
                        <a href="<?= get_the_permalink($first_post->ID); ?>" class="spot spot__wrapper"  style="background-image: url(<?php echo get_make_image_url(get_field('featured_image', $first_post->ID), 600, 600, true) ?>);">
                                <figure class="spot__bg-image"></figure>
                                <div class="spot__caption--wrapper">
                                        <h5 class="quote__reference"> <?php echo $first_post->post_title; ?> <?php echo file_get_contents(get_stylesheet_directory().'/assets/src/img/arrow.svg') ?></h5>

                                </div>
                        </a>
                </div>
            <?php } ?>
            <div class="newsarchive__col--right">
                <?php foreach ($blogposts as $blogpost):
                    // Validate each blog post before using it
                    if (!$blogpost || !$blogpost->ID || get_post_status($blogpost->ID) === false) {
                        continue;
                    }
                ?>
                    <a href="<?= get_the_permalink($blogpost->ID); ?>" class="spot spot__wrapper">
                            <figure class="spot__bg-image" style="background-image: url(<?php echo get_make_image_url(get_field('featured_image', $blogpost->ID), 600, 600, true) ?>);"></figure>
                            <div class="spot__caption--wrapper">
                                    <h5 class="quote__reference"> <?php echo $blogpost->post_title; ?> <?php echo file_get_contents(get_stylesheet_directory().'/assets/src/img/arrow.svg') ?></h5>
                            </div>
                    </a>
                <?php endforeach; ?>
            </div>
        <?php } ?>
</div>