<div id="map">


    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCUw85_MN6tobpVne5grR9pgNpnoskfGqQ&sensor=false"></script>
    <script src="<?= get_template_directory_uri(); ?>/assets/src/js/vendor/modernizr-2.6.2.min.js"></script>

    <script type="text/javascript">
        (function ($, window, undefined) {

            var isMobile = false,
                isTablet = false;

            if (Modernizr.mq("screen and (max-width:767px)")) {
                isMobile = true;
            }

            if (Modernizr.mq("screen and (max-width:1023px)")) {
                isTablet = true;
            }

            'use strict';
            var objectArray = {};

            <?php

            // Store original post
            global $post;
            $tmp_post = $post;


            // Define what post type to get objects from
            $post_type_to_use = 'objects';
            $object_post_type = $post_type_to_use;

            // Modify query to get properties
            $args = array('post_type' => $object_post_type, 'posts_per_page' => -1, 'orderby' => 'title', 'order' => 'ASC');
            foreach (get_object_taxonomies($object_post_type, 'objects') as $taxonomy) {
                if (isset($_GET[$taxonomy->name])) {
                    $args[$taxonomy->name] = $_GET[$taxonomy->name];
                }
            }



            $properties = new WP_Query($args);
            //$objects = get_posts(array( 'post_type' => 'property', 'posts_per_page' => -1, 'orderby' => 'title', 'order' => 'ASC' ));
            foreach( $properties->posts as $object ) :
            $lat_lng_acf = get_field('location', $object->ID);

            global $post;
            $object = $post;

            ?>

            objectArray['<?php echo $object->ID ?>'] = {
                <?php if(is_array($lat_lng_acf)): ?>
                coords: [<?php echo $lat_lng_acf['lat']; ?>, <?php echo $lat_lng_acf['lng']; ?>],
                <?php else: ?>
                coords: [<?php $epx_lat = explode('|', $lat_lng_acf); echo $epx_lat[1]; ?>],
                <?php endif; ?>
                title: '<?php echo $object->post_title ?>',
                <?php
                foreach (get_object_taxonomies($object_post_type, 'objects') as $taxonomy):
                    $terms = implode(", ", array_map(function ($obj) {
                        return $obj->name;
                    }, wp_get_object_terms($object->ID, $taxonomy->name)));
                    echo $taxonomy->name . ': "' . $terms . '",
                                        ';
                endforeach;
                ?>
                <?php if(is_array($lat_lng_acf)): ?>
                address: '<?php echo $lat_lng_acf['address']; ?>',
                <?php else: ?>
                address: '<?php echo $epx_lat[0]; ?>',
                <?php endif; ?>
                link: '<?php echo $object->guid; ?>',
                image: '<?php echo get_make_image_url(get_field('featured_image', $object->ID), 300, 999); ?>'
            };

            <?php endforeach; $post = $tmp_post; ?>
            google.maps.visualRefresh = true;

            function initialize() {

                // Marker image URL
                var markerImage = '<?php echo get_template_directory_uri() ?>/assets/dist/img/poi_blue.png';
                var markerActiveImage = '<?php echo get_template_directory_uri() ?>/assets/dist/img/poi_pink.png';


                var styles = [
                    {
                        featureType: 'landscape',
                        elementType: 'all',
                        stylers: [
                            {hue: '#DBEBD9'},
                            {saturation: 6},
                            {lightness: 0},
                            {visibility: 'on'}
                        ]
                    }, {
                        featureType: 'water',
                        elementType: 'all',
                        stylers: [
                            {hue: '#8ECDE8'},
                            {saturation: 39},
                            {lightness: -4},
                            {visibility: 'on'}
                        ]
                    }, {
                        featureType: 'road',
                        elementType: 'all',
                        stylers: [
                            {hue: '#FFFFFF'},
                            {saturation: -100},
                            {lightness: 100},
                            {visibility: 'on'}
                        ]
                    }, {
                        featureType: 'poi',
                        elementType: 'labels',
                        stylers: [
                            {hue: '#000000'},
                            {saturation: -100},
                            {lightness: -100},
                            {visibility: 'off'}
                        ]
                    }
                ];

                var mapOptions = {
                    center: new google.maps.LatLng(58.2861851, 12.299504800000022),
                    zoom: 12,
                    //mapTypeId: google.maps.MapTypeId.ROADMAP,
                    scrollwheel: false,
                    draggable: true,
                    mapTypeId: 'Styled'
                };

                var map = new google.maps.Map(document.getElementById("map-canvas"), mapOptions);
                var styledMapType = new google.maps.StyledMapType(styles, {name: 'Styled'});
                map.mapTypes.set('Styled', styledMapType);


                /*
                 Add markers
                 */

                var i = 0,
                    markers = [];
                for (var key in objectArray) {
                    var object = objectArray[key];


                    <?php
                    $object_taxonomy_names = array(
                        'properties' => array('type' => 'property_type', 'area' => 'property_area'),
                        'objects' => array('type' => 'object_type', 'area' => 'object_area'),
                        'projects' => array('type' => 'project_type', 'area' => 'project_area')
                    )
                    ?>

                    var objectMarker = new google.maps.Marker({
                        position: new google.maps.LatLng(object.coords[0], object.coords[1]),
                        map: map,
                        icon: markerImage,
                        itemID: i,
                        itemKey: key,
                        link: object.link,
                        image: object.image,
                        address: object.address,

                        <?php foreach ($object_taxonomy_names[$post_type_to_use] as $key => $tax) {
                        echo $key . ': object.' . $tax . ',';
                    } ?>
                        title: object.title
                    });

                    markers[i] = objectMarker;

                    /*

                     Event listener for Marker click

                     */

                    google.maps.event.addListener(objectMarker, 'click', function () {
                        highlightObject(this);
                    });

                    i++;

                }


                /* ---------------------------------

                 - Multi Touch Zoom

                 --------------------------------- */

                // On touch start
                function mapTouchStart(e) {
                    // If the number of fingers is greater or equal to 2
                    if (e.touches.length > 1) {
                        map.setOptions({
                            draggable: true
                        })
                    }
                }

                // On touch end
                function mapTouchEnd() {

                    if (touchTimer === true) {
                        touchTimer.clearTimeout();
                    }

                    var touchTimer = window.setTimeout(function () {
                        map.setOptions({
                            draggable: false
                        })
                    }, 300)
                }

                document.getElementById("map-canvas").addEventListener("touchstart", mapTouchStart, true);
                document.getElementById("map-canvas").addEventListener("touchend", mapTouchEnd, true);


                /*

                 Highlight Element in Retailers List

                 */

                function highlightObject(marker) {

                    // Reset all marker icons
                    $.each(markers, function (key, marker) {
                        marker.setIcon(markerImage);
                    });

                    // Set current marker to active
                    marker.setIcon(markerActiveImage);

                    map.panTo(marker.getPosition());
                    if (map.getZoom() < 15) {
                        map.setZoom(15);
                    }

                    var popup = $('#objectDetails');
                    popup.hide(0, function () {
                        var innerHtml = '' +
                            '<div class="inner">' +
                            '<a href="' + marker.link + '" title="' + marker.title + '">' +
                            '<div class="spot__caption--wrapper">' +
                            '<h4 class="title">' + marker.title + '</h4>' +
                            '<h5 class="address">' + marker.address + '</h5></a> ';

                        if (marker.area) {
                            innerHtml += '<div class="meta"><div class="group-label">Område:</div>' + marker.area + '</div>';
                        }


                        innerHtml += '<div class="meta"><div class="group-label">Typ av lokal:</div>' + marker.type + '</div></div></div>';

                        popup.html(innerHtml).show(0);
                    });

                }

                function findMarkerByPostID() {
                    var objectID = $('#main').data('selected-object-id');
                    for (var i = 0; i < markers.length; i++) {
                        if (markers[i].itemKey == objectID) {
                            highlightObject(markers[i]);
                            break;
                        }
                    }
                }

                findMarkerByPostID();

            }

            $(document).ready(function () {

//                        if ( isMobile === false ) {
                google.maps.event.addDomListener(window, 'load', initialize);

                $('.retailer a').on('click', function (e) {
                    if ($(this).parent().parent().hasClass('inactive-retailer')) {
                        e.preventDefault();
                    }
                });
//                        }

            });

        })(jQuery, this);

    </script>
    <div id="map-canvas"></div>
    <div id='objectDetails'></div>

</div>