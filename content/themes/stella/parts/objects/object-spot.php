<div class="object">
    <div class="object__wrapper">
        <a href="<?php the_permalink() ?>">
            <?php
            $src = (get_field('featured_image')) ? get_field('featured_image') : get_template_directory_uri() . '/assets/src/img/default-image-small.png';
            ?>
            <div class="object__image" style="background-image: url('<?= get_make_image_url($src, 560, 315, true) ?>')"></div>
            <div class="object__content">
                <div class="object__meta">
                    <?php
                    // $icons = array(
                    //     '<span class="material-icons">local_offer</span> ',
                    //     '<span class="material-icons">room</span> '
                    // );
                    $i = 0;
                    foreach (get_object_taxonomies(get_post_type(), 'objects') as $taxonomy) :
                        $terms = implode(', ', array_map(function ($term) {
                            return $term->name;
                        }, wp_get_post_terms(get_the_ID(), $taxonomy->name)));
                        if ($terms != "") :
                            echo '<span class="meta__item">';
                            echo ($terms != "") ? $icons[$i] . ' ' . $terms : '';
                            echo '</span>';
                        endif;
                        $i++;
                    endforeach;
                    ?>
                </div>
                <h5 class="object__title"><?php the_title(); ?></h5>
                <?php if (!empty(get_field('excerpt'))) : ?>
                    <p class="object__description"><?= get_field('excerpt') ?></p>
                <?php endif; ?>
            </div>
            <div class="arrow">
                    <?php echo file_get_contents(get_stylesheet_directory() . '/assets/src/img/arrow-right.svg'); ?>
            </div>
        </a>
    </div>
</div>