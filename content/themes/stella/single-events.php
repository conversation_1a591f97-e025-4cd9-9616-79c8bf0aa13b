<?php

$start_date = get_field('event_start_date');
$start_time = get_field('event_start_time');
$end_date = get_field('event_end_date');
$end_time = get_field('event_end_time');

$start_year = convertYear($start_date);
$end_year = convertYear($end_date);
$year_same = ($start_year == $end_year) ? true : false;

$location = get_field('event_location');
$contact_name = get_field('event_contact_name');
$contact_email = get_field('event_contact_email');
$contact_phone = get_field('event_contact_phone');
$link_form = get_field('event_link');
$color = get_field('event_text_color');
$placeholder_image = get_field('event_placeholder_image');

$today = strtotime(wp_date('Y-m-d H:i:s'));
$event_date = strtotime($end_date . ' ' . $end_time);
$past_event = ($event_date <= $today) ? true : false;

$contacts = get_field('event_contact');
$id = ($contacts) ? $contacts->ID : null;

?>

<!-- @Todo style post -->

<?php get_header(); ?>

<?php while (have_posts()) : the_post(); ?>
    <div class="hero-event">
        <?php if (!empty(has_post_thumbnail())) : ?>
            <?= the_post_thumbnail('full'); ?>
        <?php endif; ?>
        <?php if (empty(has_post_thumbnail())) : ?>
            <img src="<?= get_template_directory_uri(); ?>/assets/dist/img/<?= $placeholder_image; ?>.jpg" alt="<?= $placeholder_image; ?>" loading="lazy">
        <?php endif; ?>
        <div class="hero-event__content hero-event__content--<?= $color ? $color : 'white'; ?>">
            <h1><?= the_title(); ?></h1>
            <?php 
            if ($start_date !== $end_date) : ?>
                <p><?= convertDate($start_date) . (!$year_same ? ' ' . $start_year : '') ?> - <?= convertDate($end_date) . ' ' . $end_year ?>, <?= convertTime($start_time); ?> - <?= convertTime($end_time); ?></p>
            <?php else : ?>
                <p><?= convertDate($start_date) . ' ' . $start_year; ?>, <?= convertTime($start_time); ?> - <?= convertTime($end_time); ?></p>
            <?php endif; ?>

            <?php if (!$past_event && !empty($link_form)) : ?>
                <div class="hero-event__content__link">
                    <a href="<?php echo $link_form['url']; ?>"><?php echo $link_form['title']; ?></a>
                </div>
            <?php endif; ?>
        </div>
        <div class="wrap">
            <img src="<?php echo get_template_directory_uri() . '/assets/src/img/arrow-down.svg'; ?>" class="animated bounce" loading="lazy">
        </div>
    </div>
    <div class="container">


        <?php
        $eventspageUrl = getTplPageURL('tpl-events.php');
        load_module('/parts/general/previous-link-bar.php', array(
            'link_url' => $eventspageUrl,
            'link_text' => 'Tillbaka till evenemang',
        )); ?>
        <section class="content content-event">
            <article id="post-<?php the_ID(); ?>">
                <div class="single-event">
                    <?php the_content(); ?>

                    <?php if (!$past_event && !empty($link_form)) : ?>
                        <div class="hero-event__content__link">
                            <a href="<?php echo $link_form['url']; ?>"><?php echo $link_form['title']; ?></a>
                        </div>
                    <?php else : ?>
                        <div style="margin-top: 108px;"></div>
                    <?php endif; ?>
                    <div class="single-event__contact">
                        <h3>Plats</h3>
                        <p><?= $location; ?></p>
                        <h3>Har du frågor om eventet?</h3>
                        <p class='single-event__contact__title'><?= $contact_name; ?></p>
                        <p class='single-event__contact__info'><?= $contact_email; ?></p>
                        <p class='single-event__contact__info'><?= $contact_phone; ?></p>
                    </div>
                </div>
            </article>
        <?php endwhile; ?>

        </section>
    </div>


    <?php get_footer(); ?>