<?php
    // Map up URLs for map view per post type
    $base = get_site_url();
    $map_links = array(
        "properties" => $base.'/fastigheter/karta/',
        "objects" => $base.'/lediga-lokaler/karta/',
        "projects" => $base.'/byggprojekt/karta/'
    );
?>

<?php get_header(); ?>

<?php load_module('/parts/general/top.php'); ?>

<?php if( $_SERVER['HTTP_HOST'] == 'innovatum.kraftstaden.se'): ?>
    <?php load_module('/parts/general/hub-menu.php'); ?>
<?php endif; ?>

<div id="main" role="main" <?php echo (get_field('location')) ? 'data-map-link="'.$map_links[get_post_type()].'?objectid='.$post->ID.'"' : ''; ?> role="main"">
    <div class="container">
        <section class="content">
            <div class="filter-menu">
                <div class="filters">
                    <?php $back_url = ($_SERVER['HTTP_HOST'] == 'innovatum.kraftstaden.se') ? home_url('innovatum/lediga-lokaler') : get_post_type_archive_link( 'objects'); ?>
                    <a class="back" href="<?= $back_url; ?>" title="Tillbaka"><?= file_get_contents(get_stylesheet_directory().'/assets/src/img/arrow.svg') ?> Tillbaka</a>
                    <div class="filters__groups">
                        <?php foreach(get_object_taxonomies(get_post_type(), 'objects') as $taxonomy): ?>
                            <div class="group">
                                <div class="group-label"><?= $taxonomy->labels->name ?>:</div>
                                <?php $terms = wp_get_post_terms(get_the_ID(),$taxonomy->name);
                                foreach ($terms as $term) {
                                    if($terms != ""): ?>
                                        <div class="filters__pill"><?= $term->name ?></div>
                                    <?php endif;
                                }
                                $i++; ?>
                            </div>
                        <?php endforeach; ?>
                        <?php if(get_field('object_space') != ''): ?>
                            <div class="group">
                                <div class="group-label">Yta:</div>
                                <div class="filters__value"><?= get_field('object_space'); ?> m²</div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <?php while (have_posts()) : the_post(); ?>
                <article id="post-<?= get_the_ID(); ?>">
                    <?php the_content(); ?>
                </article>
            <?php endwhile;?>
        </section>
    </div>
</div>

<?php get_footer(); ?>

