<?php
    // Map up URLs for map view per post type
    $base = get_bloginfo("url");
    $map_links = array(
        "properties" => $base.'/fastigheter/karta/',
        "objects" => $base.'/lediga-lokaler/karta/',
        "projects" => $base.'/byggprojekt/karta/'
    );
?>

<?php get_header(); ?>

<?php load_module('/parts/general/top.php'); ?>
<div id="main" role="main" <?php echo (get_field('location')) ? 'data-map-link="'.$map_links[get_post_type()].'?objectid='.$post->ID.'"' : ''; ?> role="main">
    <div class="container">
        <section class="content">
            <div class="filter-menu">
                <div class="filters">    
                    <a class="back" href="<?= home_url( 'vara-fastigheter'); ?>" title="Tillbaka"><?= file_get_contents(get_stylesheet_directory().'/assets/src/img/arrow.svg') ?> Tillbaka</a>
                    <div class="info">
                        <?php
                        $icons = array(
                            '<span class="material-icons">local_offer</span> ',
                            '<span class="material-icons">room</span> '
                        );
                        $i = 0;
                        foreach(get_object_taxonomies(get_post_type(), 'objects') as $taxonomy):
                            $terms = wp_get_post_terms(get_the_ID(),$taxonomy->name);
                            foreach ($terms as $term) {
                                if($terms != ""):
                                    echo '<span class="meta__item">';
                                    echo ($term != "") ? $icons[$i] . ' ' . $term->name : '';
                                    echo '</span>';
                                endif;
                            }
                            $i++;
                        endforeach; ?>
                    </div>
                </div>
            </div>

            <?php while (have_posts()) : the_post(); ?>
                <article id="post-<?php the_ID(); ?>">
                    <?php the_content(); ?>
                </article>
            <?php endwhile; ?>
        </section>
    </div>
</div>

<?php get_footer(); ?>


