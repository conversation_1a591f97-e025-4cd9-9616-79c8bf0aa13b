<?php

$post_type_to_use = get_field('post_type');
?>
<?php get_header(); ?>
<?php $vc_content = has_shortcode($post->post_content, 'vc_row'); ?>

<section id="contact-map">
    <div class="container">

        <!-- Map -->
        <div class="row">
            <div id="map" class="col-12 hide-for-small">


                <script src="https://maps.googleapis.com/maps/api/js?sensor=false"></script>
                <script src="<?= get_template_directory_uri(); ?>/assets/js/vendor/modernizr-2.6.2.min.js"></script>

                <script type="text/javascript">
                    (function($, window, undefined) {

                        var isMobile = false,
                            isTablet = false;

                        if (Modernizr.mq("screen and (max-width:767px)")) {
                            isMobile = true;
                        }

                        if (Modernizr.mq("screen and (max-width:1023px)")) {
                            isTablet = true;
                        }

                        'use strict';
                        var objectArray = {};



                        objectArray[1] = {
                            coords: [58.2720479, 12.27552639999999],
                            title: 'Trollhättans Tomt AB',
                            link: '',
                            image: ''
                        };


                        google.maps.visualRefresh = true;

                        function initialize() {

                            // Marker image URL
                            var markerImage = '<?php echo get_template_directory_uri() ?>/assets/images/poi_blue.png';
                            var markerActiveImage = '<?php echo get_template_directory_uri() ?>/assets/images/poi_pink.png';


                            var styles = [{
                                featureType: 'landscape',
                                elementType: 'all',
                                stylers: [{
                                        hue: '#DBEBD9'
                                    },
                                    {
                                        saturation: 6
                                    },
                                    {
                                        lightness: 0
                                    },
                                    {
                                        visibility: 'on'
                                    }
                                ]
                            }, {
                                featureType: 'water',
                                elementType: 'all',
                                stylers: [{
                                        hue: '#8ECDE8'
                                    },
                                    {
                                        saturation: 39
                                    },
                                    {
                                        lightness: -4
                                    },
                                    {
                                        visibility: 'on'
                                    }
                                ]
                            }, {
                                featureType: 'road',
                                elementType: 'all',
                                stylers: [{
                                        hue: '#FFFFFF'
                                    },
                                    {
                                        saturation: -100
                                    },
                                    {
                                        lightness: 100
                                    },
                                    {
                                        visibility: 'on'
                                    }
                                ]
                            }, {
                                featureType: 'poi',
                                elementType: 'labels',
                                stylers: [{
                                        hue: '#000000'
                                    },
                                    {
                                        saturation: -100
                                    },
                                    {
                                        lightness: -100
                                    },
                                    {
                                        visibility: 'off'
                                    }
                                ]
                            }];

                            var mapOptions = {
                                center: new google.maps.LatLng(58.2861851, 12.299504800000022),
                                zoom: 12,
                                //mapTypeId: google.maps.MapTypeId.ROADMAP,
                                scrollwheel: false,
                                draggable: true,
                                mapTypeId: 'Styled'
                            };

                            var map = new google.maps.Map(document.getElementById("map-canvas"), mapOptions);
                            var styledMapType = new google.maps.StyledMapType(styles, {
                                name: 'Styled'
                            });
                            map.mapTypes.set('Styled', styledMapType);


                            /*
                             Add markers
                             */

                            var i = 0,
                                markers = [];
                            for (var key in objectArray) {
                                var object = objectArray[key];


                                var objectMarker = new google.maps.Marker({
                                    position: new google.maps.LatLng(object.coords[0], object.coords[1]),
                                    map: map,
                                    icon: markerImage,
                                    itemID: i,
                                    itemKey: key,
                                    link: object.link,
                                    image: object.image,
                                    title: object.title
                                });

                                markers[i] = objectMarker;

                                /*

                                 Event listener for Marker click

                                 */

                                google.maps.event.addListener(objectMarker, 'click', function() {
                                    highlightObject(this);
                                });

                                i++;

                            }


                            /* ---------------------------------

                             - Multi Touch Zoom

                             --------------------------------- */

                            // On touch start
                            function mapTouchStart(e) {
                                // If the number of fingers is greater or equal to 2
                                if (e.touches.length > 1) {
                                    map.setOptions({
                                        draggable: true
                                    })
                                }
                            }

                            // On touch end
                            function mapTouchEnd() {

                                if (touchTimer === true) {
                                    touchTimer.clearTimeout();
                                }

                                var touchTimer = window.setTimeout(function() {
                                    map.setOptions({
                                        draggable: false
                                    })
                                }, 300)
                            }

                            document.getElementById("map-canvas").addEventListener("touchstart", mapTouchStart, true);
                            document.getElementById("map-canvas").addEventListener("touchend", mapTouchEnd, true);


                            /*

                             Highlight Element in Retailers List

                             */

                            function highlightObject(marker) {

                                // Reset all marker icons
                                $.each(markers, function(key, marker) {
                                    marker.setIcon(markerImage);
                                });

                                // Set current marker to active
                                marker.setIcon(markerActiveImage);

                                map.panTo(marker.getPosition());
                                if (map.getZoom() < 15) {
                                    map.setZoom(15);
                                }

                                var popup = $('#objectDetails');
                                popup.hide(0, function() {
                                    popup.show(500);
                                });

                            }

                            highlightObject(markers[0]);

                        }

                        $(document).ready(function() {

                            if (isMobile === false) {
                                google.maps.event.addDomListener(window, 'load', initialize);

                                $('.retailer a').on('click', function(e) {
                                    if ($(this).parent().parent().hasClass('inactive-retailer')) {
                                        e.preventDefault();
                                    }
                                });
                            }

                        });

                    })(jQuery, this);
                </script>
                <div id="map-canvas" style="height:450px;"></div>
                <div id='objectDetails'>
                    <div class="inner">
                        <h4 class="title">Trollhättans Tomt AB</h4>
                        <dl>
                            <dt>Telefonnummer:</dt>
                            <dd><?php the_field('phone_number', 'options') ?></dd>

                            <dt>E-post:</dt>
                            <dd><?php the_field('email', 'options') ?></dd>

                            <dt>Adress:</dt>
                            <dd>
                                Flygfältsvägen 9<br />
                                461 38 Trollhättan
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section id="main" role="main">
    <div id="content">
        <?php while (have_posts()) : the_post(); ?>
            <article <?php $contain_class = ($vc_content) ? 'container' : 'container';
                        post_class($contain_class) ?> id="post-<?php the_ID(); ?>">
                <?php if (!$vc_content) echo '<h1>' . get_the_title() . '</h1>'; ?>
                <?php the_content(); ?>
            </article>
        <?php endwhile; //End the loop 
        ?>
    </div>

    <div class="container">
        <!-- Spots -->
        <?php $spots = get_field('related_small_puff');
        if ($spots) : ?>
            <div class="row  small-spots-row">
                <hr />
                <?php foreach ($spots as $spot) : ?>
                    <div class="col-<?php echo 12 / count($spots); ?>">
                        <article class="small-spot small-spot-block">
                            <a class="link" href="<?php echo get_field('url', $spot->ID) ?>" title="">
                                <img class="img" src="<?php echo wp_get_attachment_url(get_field('image', $spot->ID)); ?>" alt="<?php echo apply_filters('the_title', $spot->post_title) ?>" loading="lazy">
                                <h3 class="header h2 <?php echo get_field('color_theme', $spot->ID); ?>"><?php echo apply_filters('the_title', $spot->post_title) ?> <i class="fa fa-arrow-circle-o-right"></i></h3>
                                <div>
                                    <?php echo apply_filters('the_content', $spot->post_content) ?>
                                </div>
                            </a>
                        </article>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; // y u no spots? 
        ?>
    </div>
</section>
<?php get_footer(); ?>