<?php
/*
Template Name: <PERSON><PERSON><PERSON> restaurang
*/

acf_form_head();
get_header();
load_module('/parts/general/top.php');

$user = wp_get_current_user();
$user_name = $user->data->user_nicename;
$user_id = 'user_' . $user->data->ID;
?>


<div id="main" role="main">
    <div class="container">
        <section class="content">
            <?php
            $restaurant = get_field('restaurant_connection', $user_id);
            if (current_user_can('restaurant') && !empty($restaurant)) :
                $restaurant_id = get_field('restaurant_connection', $user_id)[0]->ID;

                // Validate restaurant ID before using it
                if (!$restaurant_id || get_post_status($restaurant_id) === false) {
                    error_log('Invalid restaurant ID: ' . $restaurant_id);
                    echo '<p>Error: Invalid restaurant connection.</p>';
                    return;
                }
            ?>
                <h1>Uppdatera din restaurang</h1>
                <div class="actions">
                    <a class="button" href="<?= wp_logout_url(); ?>">Logga ut</a>
                    <a class="button" href="<?= get_the_permalink($restaurant_id); ?>">Visa restaurangsida</a>
                </div>
                <?php
                acf_form(array(
                    'post_id'        => $restaurant_id,
                    'post_title'    => false,
                    'updated_message' => __("Restaurang uppdaterad", 'acf'),
                    'submit_value'    => 'Uppdatera',                
                ));
                if($_GET["updated"] === "true") :
                  do_action( 'litespeed_purge_post', $restaurant_id );
                endif;
                ?>
            <?php endif; ?>            
        </section>
    </div>
</div>

<?php get_footer(); ?>