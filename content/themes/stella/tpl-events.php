<?php
/*
Template Name: Eventsida
*/

$today = date('Y-m-d H:i:s');


?>

<?php get_header(); ?>
<?php load_module('/parts/general/top.php'); ?>

<div id="main" role="main">
    <div class="container">
        <section class="content">
            <div class="events">
                <?php

                $categories = get_terms([
                    'taxonomy' => 'event_category',
                    'hide_empty' => true,
                ]);

                $categories_ids = !empty($_GET['event_category']) && is_array($_GET['event_category']) ? $_GET['event_category'] : array();
                $categories_hierarchicaly = [];
                sort_terms_hierarchicaly($categories, $categories_hierarchicaly);

                function is_category_selected($category, $categories_ids)
                {
                    $category_ids = $categories_ids;
                    if (in_array($category->term_id, $categories_ids)) {
                        $category_ids = array_diff($category_ids, [$category->term_id]);
                    } else {
                        array_push($category_ids, $category->term_id);
                    }
                    return $category_ids;
                };

                ?>
                <div id='filter' class="events__filter">
                    <select name="category_select" class="category_select">
                        <option value="" disabled selected>Kategori</option>
                        <option value="<?= remove_query_arg('event_category') ?>">Alla kategorier</option>
                        <?php foreach ($categories_hierarchicaly as $category) :
                            $category_ids = is_category_selected($category, $categories_ids);
                        ?>
                            <option <?= (!empty($_GET['event_category']) && $_GET['event_category'] == $category_ids[0]) ? 'selected="selected"' : ''; ?> value="<?= add_query_arg('event_category', $category->term_id) . "#filter"; ?>"><?= $category->name; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <?php
                $page_title = 'Event';
                if (!empty($_GET['event_category']) && $_GET['event_category']) {
                    $category = get_term($_GET['event_category'], 'event_category');
                    $page_title .= ' - ' . $category->name;
                }
                ?>
                <h1 class="events__current"><?= $page_title; ?></h1>

                <?php
                $all_args = array(
                    'post_type'         => 'events',
                    'posts_per_page' => -1,
                    'orderby' => 'meta_value',
                    'order' => 'ASC',
                    'meta_key'  => 'event_start',
                    'meta_query'        => array(
                        'relation' => 'AND',
                        array(
                            'key'       => 'event_end',
                            'compare'   => '>=',
                            'value'     => $today,
                            'type' => 'DATETIME',
                        ),
                    ),
                );

                if (!empty($_GET['event_category']) && $_GET['event_category']) {
                    $all_args['tax_query'][] = [
                        'taxonomy' => 'event_category',
                        'field' => 'term_id',
                        'terms' => $_GET['event_category']
                    ];
                }

                $all_query = new WP_Query($all_args);

                $past_args = array(
                    'post_type' => 'events',
                    'posts_per_page' => -1,
                    'orderby' => 'meta_value',
                    'order' => 'DESC',
                    'meta_key' => 'event_end',
                    'meta_query' => array(
                        'relation' => 'AND',
                        array(
                            'key' => 'event_end',
                            'compare' => '<=',
                            'value' => $today,
                            'type' => 'DATETIME',
                        ),
                    ),
                );

                if (!empty($_GET['event_category']) && $_GET['event_category']) {
                    $past_args['tax_query'][] = [
                        'taxonomy' => 'event_category',
                        'field' => 'term_id',
                        'terms' => $_GET['event_category']
                    ];
                }

                $past_query = new WP_Query($past_args);

                ?>
                <?php if ($all_query->have_posts() && $all_query->post_count) : ?>
                    <div class="events__container">
                        <?php while ($all_query->have_posts()) : $all_query->the_post();
                            $id = get_the_ID();

                            // Validate post ID and post status
                            if (!$id || get_post_status($id) === false) {
                                error_log('Invalid or non-existent post ID: ' . $id);
                                continue;
                            }

                            // Check if this is a FeedWordPress syndicated post and skip if problematic
                            $post_meta = get_post_meta($id, 'syndication_feed', true);
                            if (!empty($post_meta)) {
                                // Skip all FeedWordPress posts that might have corrupted data
                                error_log('Skipping FeedWordPress syndicated post ID: ' . $id);
                                continue;
                            }

                            $start_date = get_field('event_start_date', $id);
                            $start_time = get_field('event_start_time', $id);
                            $end_date = get_field('event_end_date', $id);
                            $end_time = get_field('event_end_time', $id);

                            $start_year = convertYear($start_date);
                            $end_year = convertYear($end_date);
                            $year_same = ($start_year == $end_year) ? true : false;

                            $title = get_the_title($id);

                            // Add error handling for permalink to prevent fatal errors with corrupted posts
                            // For non-syndicated posts, completely disable FeedWordPress permalink filter
                            $feedwordpress_filters_removed = false;
                            $is_syndicated = function_exists('is_syndicated') ? is_syndicated($id) : false;

                            if (!$is_syndicated && function_exists('remove_filter') && has_filter('post_type_link', 'syndication_permalink')) {
                                remove_filter('post_type_link', 'syndication_permalink');
                                $feedwordpress_filters_removed = true;
                            }

                            try {
                                $link = get_the_permalink($id);
                            } catch (Exception $e) {
                                // If permalink fails, skip this post
                                error_log('Error getting permalink for post ID ' . $id . ': ' . $e->getMessage());
                                // Re-add the filter before continuing
                                if ($feedwordpress_filters_removed && function_exists('add_filter')) {
                                    add_filter('post_type_link', 'syndication_permalink', 10, 4);
                                }
                                continue;
                            } catch (TypeError $e) {
                                // Handle TypeError from FeedWordPress plugin
                                error_log('TypeError getting permalink for post ID ' . $id . ': ' . $e->getMessage());
                                // Re-add the filter before continuing
                                if ($feedwordpress_filters_removed && function_exists('add_filter')) {
                                    add_filter('post_type_link', 'syndication_permalink', 10, 4);
                                }
                                continue;
                            }

                            // Re-add the filter if we removed it
                            if ($feedwordpress_filters_removed && function_exists('add_filter')) {
                                add_filter('post_type_link', 'syndication_permalink', 10, 4);
                            }

                            $excerpt = get_the_excerpt($id);
                            add_filter('wp_calculate_image_srcset', 'remove_sizes_from_srcset', 10, 5);
                            $image = get_the_post_thumbnail($id, 'medium_large', ['loading' => 'lazy']);
                            remove_filter('wp_calculate_image_srcset', 'remove_sizes_from_srcset', 10);
                            $categories = wp_get_post_terms($id, 'event_category');
                            $placeholder_image = get_field('event_placeholder_image');

                        ?>
                            <div class="events__single">
                                <a href="<?= $link; ?>">
                                    <div class="events__single__image">
                                        <?php if (!empty($image)) : ?>
                                            <?= $image; ?>
                                        <?php endif; ?>
                                        <?php if (!$image) : ?>
                                            <img src="<?= get_template_directory_uri(); ?>/assets/dist/img/<?= $placeholder_image; ?>.jpg" alt="<?= $placeholder_image; ?>" loading="lazy">
                                        <?php endif; ?>
                                        <div class="events__single__image__flag">
                                            <?php if ($start_date > $today) : ?>
                                                <span>Kommande</span>
                                            <?php elseif ($start_date < $today && $end_date > $today || $start_date == $today || $end_date == $today) : ?>
                                                <span>Idag</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="events__single__text">
                                        <div class='events__single__text__category'>
                                            <?php foreach ($categories as $category) : ?>
                                                <span><?= $category->name; ?></span>
                                            <?php endforeach; ?>
                                        </div>
                                        <h2>
                                            <?= $title; ?>
                                        </h2>
                                        <?php if ($start_date !== $end_date) : ?>
                                            <p><?= convertDate($start_date) . (!$year_same ? ' ' . $start_year : '') ?> - <?= convertDate($end_date) . ' ' . $end_year ?>, <?= convertTime($start_time); ?> - <?= convertTime($end_time); ?></p>
                                        <?php else : ?>
                                            <p><?= convertDate($start_date) . ' ' . $start_year; ?>, <?= convertTime($start_time); ?> - <?= convertTime($end_time); ?></p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="events__single__arrow arrow">
                                        <?php echo file_get_contents(get_stylesheet_directory() . '/assets/src/img/arrow-right.svg'); ?>
                                    </div>
                                </a>
                            </div>
                        <?php endwhile; ?>
                    </div>
                <?php endif; ?>
                <?php wp_reset_query(); ?>
                <?php if (!$all_query->post_count) : ?>
                    <div class="events__container">
                        <div>
                            <p>Inga event hittades.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            <?php if ($past_query->have_posts() && $past_query->post_count) : ?>
                <h2 class="events__past">Tidigare event</h2>
                <div class="events__container">
                    <?php while ($past_query->have_posts()) : $past_query->the_post();
                        $id = get_the_ID();

                        // Validate post ID and post status
                        if (!$id || get_post_status($id) === false) {
                            error_log('Invalid or non-existent post ID: ' . $id);
                            continue;
                        }

                        // Check if this is a FeedWordPress syndicated post and skip if problematic
                        $post_meta = get_post_meta($id, 'syndication_feed', true);
                        if (!empty($post_meta)) {
                            // Skip all FeedWordPress posts that might have corrupted data
                            error_log('Skipping FeedWordPress syndicated post ID: ' . $id);
                            continue;
                        }

                        $start_date = get_field('event_start_date', $id);
                        $start_time = get_field('event_start_time', $id);
                        $end_date = get_field('event_end_date', $id);
                        $end_time = get_field('event_end_time', $id);

                        $start_year = convertYear($start_date);
                        $end_year = convertYear($end_date);
                        $year_same = ($start_year == $end_year) ? true : false;

                        $title = get_the_title($id);

                        // Add error handling for permalink to prevent fatal errors with corrupted posts
                        // For non-syndicated posts, completely disable FeedWordPress permalink filter
                        $feedwordpress_filters_removed = false;
                        $is_syndicated = function_exists('is_syndicated') ? is_syndicated($id) : false;

                        if (!$is_syndicated && function_exists('remove_filter') && has_filter('post_type_link', 'syndication_permalink')) {
                            remove_filter('post_type_link', 'syndication_permalink');
                            $feedwordpress_filters_removed = true;
                        }

                        try {
                            $link = get_the_permalink($id);
                        } catch (Exception $e) {
                            // If permalink fails, skip this post
                            error_log('Error getting permalink for post ID ' . $id . ': ' . $e->getMessage());
                            // Re-add the filter before continuing
                            if ($feedwordpress_filters_removed && function_exists('add_filter')) {
                                add_filter('post_type_link', 'syndication_permalink', 10, 4);
                            }
                            continue;
                        } catch (TypeError $e) {
                            // Handle TypeError from FeedWordPress plugin
                            error_log('TypeError getting permalink for post ID ' . $id . ': ' . $e->getMessage());
                            // Re-add the filter before continuing
                            if ($feedwordpress_filters_removed && function_exists('add_filter')) {
                                add_filter('post_type_link', 'syndication_permalink', 10, 4);
                            }
                            continue;
                        }

                        // Re-add the filter if we removed it
                        if ($feedwordpress_filters_removed && function_exists('add_filter')) {
                            add_filter('post_type_link', 'syndication_permalink', 10, 4);
                        }

                        $excerpt = get_the_excerpt($id);
                        add_filter('wp_calculate_image_srcset', 'remove_sizes_from_srcset', 10, 5);
                        $image = get_the_post_thumbnail($id, 'medium_large', ['loading' => 'lazy']);
                        remove_filter('wp_calculate_image_srcset', 'remove_sizes_from_srcset', 10);
                        $categories = wp_get_post_terms($id, 'event_category');
                        $placeholder_image = get_field('event_placeholder_image');
                       
                    ?>
                        <div class="events__single">
                            <a href="<?= $link; ?>">
                                <div class="events__single__image">
                                    <?php if (!empty($image)) : ?>
                                        <?= $image; ?>
                                    <?php endif; ?>
                                    <?php if (!$image) : ?>
                                        <img src="<?= get_template_directory_uri(); ?>/assets/dist/img/<?= $placeholder_image; ?>.jpg" alt="<?= $placeholder_image; ?>" loading="lazy">
                                    <?php endif; ?>
                                    <div class="events__single__image__flag">
                                        <span>Passerat</span>
                                    </div>
                                </div>
                                <div class="events__single__text">
                                    <div class='events__single__text__category'>
                                        <?php foreach ($categories as $category) : ?>
                                            <span><?= $category->name; ?></span>
                                        <?php endforeach; ?>
                                    </div>
                                    <h2>
                                        <?= $title; ?>
                                    </h2>
                                    <?php if ($start_date !== $end_date) : ?>
                                        <p><?= convertDate($start_date) . (!$year_same ? ' ' . $start_year : '') ?> - <?= convertDate($end_date) . ' ' . $end_year ?>, <?= convertTime($start_time); ?> - <?= convertTime($end_time); ?></p>
                                    <?php else : ?>
                                        <p><?= convertDate($start_date) . ' ' . $start_year; ?>, <?= convertTime($start_time); ?> - <?= convertTime($end_time); ?></p>
                                    <?php endif; ?>
                                </div>
                                <div class="events__single__arrow arrow">
                                    <?php echo file_get_contents(get_stylesheet_directory() . '/assets/src/img/arrow-right.svg'); ?>
                                </div>
                            </a>
                        </div>
                    <?php endwhile; ?>
                </div>
            <?php endif; ?>
        </section>
    </div>
</div>

<?php get_footer(); ?>