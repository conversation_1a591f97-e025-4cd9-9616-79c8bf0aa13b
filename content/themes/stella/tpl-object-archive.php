<?php
/*
Template Name: Lokaler
*/


$post_type_to_use = get_field('post_type');
if ($post_type_to_use == 'properties') :
    $types_to_filter = get_field('property_types');
    $type_tax = 'property_type';
    $areas_to_filter = get_field('property_areas');
    $area_tax = 'property_area';
elseif ($post_type_to_use == 'objects') :
    $types_to_filter = get_field('object_types');
    $type_tax = 'object_type';
    $areas_to_filter = get_field('object_areas');
    $area_tax = 'object_area';
elseif ($post_type_to_use == 'projects') :
    $types_to_filter = get_field('project_types');
    $type_tax = 'project_type';
    $areas_to_filter = get_field('project_areas');
    $area_tax = 'project_area';
endif;
?>
<?php get_header(); ?>

<?php load_module('/parts/general/top.php'); ?>
<div id="main" role="main">
    <div class="container">
        <section class="content">

            <?php while (have_posts()) : the_post(); ?>
                <?php global $post; ?>
                <article id="post-<?php the_ID(); ?>">
                    <?php the_content(); ?>
                </article>
            <?php endwhile; ?>


            <?php if (empty($types_to_filter) && empty($areas_to_filter)) : ?>
                <div class="filters">
                    <?php foreach (get_object_taxonomies($post_type_to_use, 'objects') as $taxonomy) : ?>

                        <div class="group">
                            <div class="group-label"><?= $taxonomy->labels->name ?>:</div>
                            <ul>
                                <li class="<?= (!isset($_GET[$taxonomy->name])) ? 'active' : '' ?>">
                                    <a href="<?= add_query_arg($taxonomy->name, false); ?>" alt="Alla <?= $taxonomy->labels->name ?>" title="Alla <?= $taxonomy->labels->name ?>">Alla</a>
                                </li>
                                <?php foreach (get_terms($taxonomy->name) as $term) : ?>
                                    <li class="<?= ($_GET[$taxonomy->name] == $term->slug) ? 'active' : ''; ?>">
                                        <a href="<?= add_query_arg($taxonomy->name, $term->slug); ?>" title=""><?= $term->name; ?></a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <?php $display_class = (!empty(get_field('object_display'))) ? get_field('object_display') : ''; ?>
            <div class="objects <?= $display_class; ?>">
                <?php
                global $post;
                $tmp_post = $post;

                // Modify query to get objects
                $args = array('post_type' => $post_type_to_use, 'posts_per_page' => 999, 'orderby' => 'menu_order', 'order' => 'ASC', 'post_status' => 'publish');
                foreach (get_object_taxonomies($post_type_to_use, 'objects') as $taxonomy) {
                    if (isset($_GET[$taxonomy->name])) {
                        $args[$taxonomy->name] = $_GET[$taxonomy->name];
                    }
                }

                //$args[$taxonomy->name] = $_GET[$taxonomy->name];

                if (!empty($types_to_filter)) :
                    $args[$type_tax] = $types_to_filter->slug;
                endif;
                if (!empty($areas_to_filter)) :
                    $args[$area_tax] = $areas_to_filter->slug;
                endif;


                $objects = new WP_Query($args);
                $rows = array_chunk($objects->posts, 2);

                foreach ($rows as $posts) {
                    foreach ($posts as $post) {
                        setup_postdata($post);
                        get_template_part('/parts/objects/object-spot');
                    }
                }

                // Reset to page object instead of latest property
                $post = $tmp_post;
                ?>
            </div>

        </section>
    </div>
</div>

<?php get_footer(); ?>