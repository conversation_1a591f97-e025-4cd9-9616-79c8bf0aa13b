<?php

(get_field('webcam_url')) ? $webcam_url = get_field('webcam_url') : $webcam_url = false;
?>

<?php get_header(); ?>
<figure class="bg-image" style=" background-image: url('<?= make_image($theme_image, 1600, 1600, true); ?>')">
    <div style="background-color: <?= $theme_color; ?> !important;"></div>
</figure>
<?php $vc_content = has_shortcode($post->post_content, 'vc_row'); ?>
<section id="main" role="main">
    <div id="content">
        <?php while (have_posts()) : the_post(); ?>
            <article <?php $contain_class = ($vc_content) ? 'container' : 'container'; post_class($contain_class) ?> id="post-<?php the_ID(); ?>">
                <a id="back" href="<?php echo home_url('/'); ?>" style="background-color: <?= $theme_accent_1; ?>; color: <?= $theme_accent_2; ?>" title="Tillbaka till tomtab.se">‹ Tillbaka till tomtab.se</a>
                <?php if(!$vc_content) echo '<h1>'.get_the_title().'</h1>'; ?>
                <?php the_content(); ?>
            </article>
        <?php endwhile; //End the loop ?>
    </div>

    <div class="container">
        <!-- Spots -->
        <?php $spots = get_field('related_small_puff'); if($spots): ?>
            <div class="row small-spots-row">
                <hr />
                <?php foreach($spots as $spot): ?>
                    <div class="col-<?php echo 12 / count($spots); ?>">
                        <article class="small-spot small-spot-block">
                            <a class="link" href="<?php echo get_field('url',$spot->ID) ?>" title="">
                                <img class="img" src="<?php echo wp_get_attachment_url(get_field('image', $spot->ID)); ?>" alt="<?php echo apply_filters('the_title',$spot->post_title) ?>" loading="lazy">
                                <h3 class="header h2 <?php echo get_field('color_theme',$spot->ID); ?>"><?php echo apply_filters('the_title',$spot->post_title) ?> <i class="fa fa-arrow-circle-o-right"></i></h3>
                                <div>
                                    <?php echo apply_filters('the_content',$spot->post_content) ?>
                                </div>
                            </a>
                        </article>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; // y u no spots? ?>
    </div>
</section>
<?php get_footer(); ?>
